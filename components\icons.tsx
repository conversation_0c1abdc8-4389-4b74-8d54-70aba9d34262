import React from 'react';

export const BrainCircuitIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
        <path d="M12 5a3 3 0 1 0-5.993.142" />
        <path d="M18 5a3 3 0 1 0-5.993.142" />
        <path d="M12 12a3 3 0 1 0-5.993.142" />
        <path d="M18 12a3 3 0 1 0-5.993.142" />
        <path d="M12 19a3 3 0 1 0-5.993.142" />
        <path d="M18 19a3 3 0 1 0-5.993.142" />
        <path d="M12 5a3 3 0 0 0 5.993.142" />
        <path d="M12 12a3 3 0 0 0 5.993.142" />
        <path d="M12 19a3 3 0 0 0 5.993.142" />
        <path d="M12 5a3 3 0 0 1-5.993.142" />
        <path d="M12 12a3 3 0 0 1-5.993.142" />
        <path d="M12 19a3 3 0 0 1-5.993.142" />
        <path d="M9 7.5v-1" />
        <path d="M15 7.5v-1" />
        <path d="m6.007 9.5-.5-.5" />
        <path d="m18.007 9.5-.5-.5" />
        <path d="m6.007 14.5-.5-.5" />
        <path d="m18.007 14.5-.5-.5" />
        <path d="M9 14.5v-1" />
        <path d="M15 14.5v-1" />
        <path d="M9 21.5v-1" />
        <path d="M15 21.5v-1" />
        <path d="M6.5 17H6" />
        <path d="M18.5 17H18" />
        <path d="M6.5 12H6" />
        <path d="M18.5 12H18" />
        <path d="M6.5 7H6" />
        <path d="M18.5 7H18" />
        <path d="m9.5 9.5.5-.5" />
        <path d="m15.5 9.5.5-.5" />
        <path d="m9.5 14.5.5-.5" />
        <path d="m15.5 14.5.5-.5" />
        <path d="m9.5 19.5.5-.5" />
        <path d="m15.5 19.5.5-.5" />
    </svg>
);

export const InfoIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="12" y1="16" x2="12" y2="12"></line>
    <line x1="12" y1="8" x2="12.01" y2="8"></line>
  </svg>
);

export const LockIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
    </svg>
);